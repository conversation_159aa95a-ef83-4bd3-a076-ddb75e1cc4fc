#include "osa_old_api.h"
#include "ql_gpio.h"
#include <stdio.h>
#include <stdbool.h>

/* LED GPIO 映射 - 根据硬件调整 */
#define LED_GPIO_NET_RED GPIO_1
#define LED_GPIO_NET_GREEN GPIO_2
#define LED_GPIO_WIFI_GREEN GPIO_3

#define LED_TASK_STACK_SIZE (1024)
#define LED_TASK_PRIORITY (26)
#define LED_TASK_NAME "app_led_task"

#define STATUS_TASK_STACK_SIZE (1024)
#define STATUS_TASK_PRIORITY (24)
#define STATUS_TASK_NAME "app_status_task"

#define LED_EVENT_QUEUE_SIZE (10)

/* 时间辅助定义 - 在此平台上 1 tick = 5 ms */
#define MS_TO_TICK(ms) (((ms) + 4) / 5)
#define BLINK_1HZ_PERIOD_TICK MS_TO_TICK(1000)  /* 1 s */
#define TASK_SLEEP_POLL_TICK MS_TO_TICK(300)    /* 300 ms */
#define TASK_EXIT_TIMEOUT_TICK MS_TO_TICK(2000) /* 2000 ms */
#define TASK_SLEEP_MIN_TICK (1)                 /* 1 tick */

/* LED 行为定义 */
typedef enum
{
    LED_OFF = 0,
    LED_ON,
    LED_BLINK_1HZ,
} app_led_status_e;

/* 网络和 Wi-Fi 状态  */
typedef enum
{
    NET_STATUS_NO_SIM = 0,
    NET_STATUS_NO_REG,
    NET_STATUS_REG_GOOD,
    NET_STATUS_REG_POOR,
} app_net_status_e;

typedef enum
{
    WIFI_STATUS_OFF = 0,
    WIFI_STATUS_ON,
} app_wifi_status_e;

typedef enum
{
    LED_EVENT_NET_STATUS_CHANGED = 0x01,
    LED_EVENT_WIFI_STATUS_CHANGED = 0x02,
} app_led_event_e;

/* 私有描述符 - 每个 LED 一个 */
typedef struct
{
    ql_gpio_num_e gpio;      /* 硬件引脚 */
    app_led_status_e status; /* 期望行为 */
} led_desc_t;

typedef struct
{
    app_led_event_e event_type;
    union {
        app_net_status_e net_status;
        app_wifi_status_e wifi_status;
    } data;
} app_led_event_msg_t;

/* LED 表 - 顺序很重要 (见下面的枚举) */
static led_desc_t s_leds[] = {
    {LED_GPIO_NET_RED, LED_OFF},
    {LED_GPIO_NET_GREEN, LED_OFF},
    {LED_GPIO_WIFI_GREEN, LED_OFF},
};

enum
{
    LED_IDX_NET_RED = 0,
    LED_IDX_NET_GREEN,
    LED_IDX_WIFI_GREEN,
    LED_COUNT
};

static OSAMsgQRef s_evt_q = NULL;
static volatile bool s_exit_flag = false; /* 共享退出标志 */

/* 静态分配栈 */
static uint8_t s_led_task_stack[LED_TASK_STACK_SIZE];
static OSTaskRef s_led_task_ref = NULL;

static uint8_t s_status_task_stack[STATUS_TASK_STACK_SIZE];
static OSTaskRef s_status_task_ref = NULL;

/*----------------------------------------------------------------------------*/
/*                         GPIO 辅助函数 / 错误日志                           */
/*----------------------------------------------------------------------------*/
static inline void gpio_set(ql_gpio_num_e gpio, int high)
{
    ql_gpio_set_level(gpio, high ? GPIO_HIGH : GPIO_LOW);
}

static inline void log_error(const char *msg)
{
    RTI_LOG("[LED] %s\n", msg);
}

/*----------------------------------------------------------------------------*/
/*                      设置超级状态的公共辅助函数                            */
/*----------------------------------------------------------------------------*/
static void apply_net_state(app_net_status_e state)
{
    switch (state)
    {
    case NET_STATUS_NO_SIM: /* 红灯亮，绿灯灭 */
        s_leds[LED_IDX_NET_RED].status = LED_ON;
        s_leds[LED_IDX_NET_GREEN].status = LED_OFF;
        break;
    case NET_STATUS_NO_REG: /* 红灯闪烁，绿灯灭 */
        s_leds[LED_IDX_NET_RED].status = LED_BLINK_1HZ;
        s_leds[LED_IDX_NET_GREEN].status = LED_OFF;
        break;
    case NET_STATUS_REG_GOOD: /* 红灯灭，绿灯亮 */
        s_leds[LED_IDX_NET_RED].status = LED_OFF;
        s_leds[LED_IDX_NET_GREEN].status = LED_ON;
        break;
    case NET_STATUS_REG_POOR: /* 红灯灭，绿灯闪烁 */
        s_leds[LED_IDX_NET_RED].status = LED_OFF;
        s_leds[LED_IDX_NET_GREEN].status = LED_BLINK_1HZ;
        break;
    default:
        break;
    }
}

static void apply_wifi_state(app_wifi_status_e state)
{
    s_leds[LED_IDX_WIFI_GREEN].status = (state == WIFI_STATUS_ON) ? LED_ON : LED_OFF;
}

static void led_apply_level(unsigned idx, int blink_on)
{
    static int last_gpio_state[LED_COUNT] = {GPIO_LOW, GPIO_LOW, GPIO_LOW};
    int new_state;

    switch (s_leds[idx].status)
    {
    default:
    case LED_OFF:
        new_state = GPIO_LOW;
        break;
    case LED_ON:
        new_state = GPIO_HIGH;
        break;
    case LED_BLINK_1HZ:
        new_state = blink_on;
        break;
    }

    if (new_state != last_gpio_state[idx])
    {
        gpio_set(s_leds[idx].gpio, new_state);
        last_gpio_state[idx] = new_state;
    }
}

/*----------------------------------------------------------------------------*/
/*                               LED RTOS 任务                                */
/*----------------------------------------------------------------------------*/
static void led_task(void *arg)
{
    uint32_t last_toggle_tick = OSAGetTicks();
    const uint32_t period = BLINK_1HZ_PERIOD_TICK;
    int blink_on = 0;

    while (!s_exit_flag) /* 检查退出标志 */
    {
        uint32_t now = OSAGetTicks();
        uint32_t elapsed = now - last_toggle_tick;
        uint32_t to_next = (elapsed >= period) ? 0 : (period - elapsed);

        app_led_event_msg_t msg;
        int rc = OSAMsgQRecv(s_evt_q, (UINT8 *)&msg, sizeof(msg), to_next);

        if (rc == OS_SUCCESS)
        {
            do
            {
                switch (msg.event_type)
                {
                case LED_EVENT_NET_STATUS_CHANGED:
                    apply_net_state(msg.data.net_status);
                    break;
                case LED_EVENT_WIFI_STATUS_CHANGED:
                    apply_wifi_state(msg.data.wifi_status);
                    break;
                }
            } while (OSAMsgQRecv(s_evt_q, (UINT8 *)&msg, sizeof(msg), OS_NO_SUSPEND) == OS_SUCCESS);
        }
        else
        {
            blink_on = !blink_on;
            last_toggle_tick = OSAGetTicks();
        }

        for (int i = 0; i < LED_COUNT; i++)
            led_apply_level(i, blink_on);
    }

    /* 退出前关闭所有LED */
    for (int i = 0; i < LED_COUNT; i++)
    {
        s_leds[i].status = LED_OFF;
        led_apply_level(i, blink_on);
    }

    s_led_task_ref = NULL;
    OSATaskDelete(NULL);
}

/*----------------------------------------------------------------------------*/
/*                           状态轮询任务                                      */
/*----------------------------------------------------------------------------*/
static void status_task(void *arg)
{
    app_net_status_e last_net = (app_net_status_e)-1;
    app_wifi_status_e last_wifi = (app_wifi_status_e)-1;
    app_net_status_e net;
    app_wifi_status_e wifi;
    app_led_event_msg_t msg;

    while (!s_exit_flag) /* 检查退出标志 */
    {
        net = check_network_status();
        wifi = check_wifi_status();

        if (net != last_net)
        {
            msg.event_type = LED_EVENT_NET_STATUS_CHANGED;
            msg.data.net_status = net;
            OSAMsgQSend(s_evt_q, sizeof(app_led_event_msg_t), (UINT8 *)&msg, OS_NO_SUSPEND);
            last_net = net;
        }

        if (wifi != last_wifi)
        {
            msg.event_type = LED_EVENT_WIFI_STATUS_CHANGED;
            msg.data.wifi_status = wifi;
            OSAMsgQSend(s_evt_q, sizeof(app_led_event_msg_t), (UINT8 *)&msg, OS_NO_SUSPEND);
            last_wifi = wifi;
        }

        OSATaskSleep(TASK_SLEEP_POLL_TICK);
    }

    s_status_task_ref = NULL;
    OSATaskDelete(NULL);
}

static void cleanup_resources(void)
{
    /* 先确保设置退出标志，防止任务无限阻塞 */
    s_exit_flag = true;

    /* 等待任务自行退出，或超时后强制删除 */
    uint32_t start = OSAGetTicks();
    while ((s_led_task_ref || s_status_task_ref) &&
           ((OSAGetTicks() - start) < TASK_EXIT_TIMEOUT_TICK))
    {
        OSATaskSleep(TASK_SLEEP_MIN_TICK);
    }
    if (s_led_task_ref)
    {
        OSATaskDelete(s_led_task_ref);
        s_led_task_ref = NULL;
    }
    if (s_status_task_ref)
    {
        OSATaskDelete(s_status_task_ref);
        s_status_task_ref = NULL;
    }
    /* 清理消息队列 */
    if (s_evt_q)
    {
        OSAMsgQDelete(s_evt_q);
        s_evt_q = NULL;
    }
}

int app_led_init(void)
{
    /* 1) 防止重复 init */
    if (s_evt_q)
    {
        return 0;
    }

    /* 2) 初始化 GPIO，引脚初始全低 */
    for (unsigned i = 0; i < LED_COUNT; ++i)
    {
        if (ql_gpio_init(s_leds[i].gpio, GPIO_OUTPUT, PULL_NONE, GPIO_LOW) != QL_GPIO_SUCCESS)
        {
            log_error("gpio init failed");
            return -1;
        }
    }

    /* 确保初始化前退出标志为false */
    s_exit_flag = false;

    /* 3) 创建事件队列 */
    if (OSAMsgQCreate(&s_evt_q, "app_led_q", sizeof(app_led_event_msg_t),
                      LED_EVENT_QUEUE_SIZE, OSA_FIFO) != OS_SUCCESS)
    {
        log_error("create queue failed");
        goto fail;
    }

    /* 4) 创建 led_task */
    if (OSATaskCreate(&s_led_task_ref, s_led_task_stack, LED_TASK_STACK_SIZE,
                      LED_TASK_PRIORITY, LED_TASK_NAME, led_task, NULL) != OS_SUCCESS)
    {
        log_error("create led task failed");
        goto fail;
    }

    /* 5) 创建 status_task */
    if (OSATaskCreate(&s_status_task_ref, s_status_task_stack, STATUS_TASK_STACK_SIZE,
                      STATUS_TASK_PRIORITY, STATUS_TASK_NAME, status_task, NULL) != OS_SUCCESS)
    {
        log_error("create status task failed");
        goto fail;
    }

    return 0;

fail:
    /* 清理所有资源 */
    cleanup_resources();
    return -1;
}

int app_led_deinit(void)
{
    if (!s_evt_q)
    {
        return 0;
    }

    /* 清理所有资源 */
    cleanup_resources();
    return 0;
}

static app_net_status_e check_network_status(void)
{
    /* TODO: 替换为实际平台API */
    return NET_STATUS_NO_REG; /* 默认值 */
}

static app_wifi_status_e check_wifi_status(void)
{
    /* TODO: 替换为实际平台API */
    return WIFI_STATUS_OFF; /* 默认值 */
}
