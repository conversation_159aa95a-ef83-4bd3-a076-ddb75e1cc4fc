#include <stdlib.h>
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include "ql_iic.h"
#include "../inc/sc8906x_charger.h"

#define SC8960X_DRIVER_VERSION              "1.0.0_SC"

#define SC89601D_PN                         0x03

#define SC8960X_IIC_ADDR                    0x6B
#define SC8960X_IIC_NUM                     0  // 0:GPIO_49 SCL,GPIO_50 SDA

#define pr_info(format, ...) RTI_LOG("SC8960X INFO: "format, ##__VA_ARGS__)
#define pr_debug(format, ...) RTI_LOG("SC8960X DBG: "format, ##__VA_ARGS__)
#define pr_err(format, ...) RTI_LOG("SC8960X ERR: "format, ##__VA_ARGS__)

struct sc8960x_desc {
    int vindpm;
    int iindpm;
    int ichg;
    int vbat;
    int iprechg;
    int iterm;

    enum vboost boostv; // options are 4850,
    enum iboost boosti; // options are 500mA, 1200mA
    enum vac_ovp vacovp;
    int ict;
};

static struct sc8960x_desc default_desc = {
    .vindpm = 4500,
    .iindpm = 2000,
    .ichg = 2000,
    .vbat = 4200,
    .iprechg = 180,
    .iterm = 180,
    .boostv = BOOSTV_5000,
    .vacovp = VAC_OVP_14000,
    .ict = 3200,
};

struct sc8960x_chip {
    unsigned char i2c_channel;
    struct sc8960x_desc *desc;

    int pn_num;
};


static struct sc8960x_chip __chip;
static struct sc8960x_chip *chip = &__chip;

//---------------i2c api---------------
static int sc8960x_write_block(struct sc8960x_chip *chip, 
        uint8_t addr, uint8_t *data, uint8_t len)
{
    int ret;

    // 使用Quectel I2C接口写入数据
    ret = ql_i2c_write(chip->i2c_channel, SC8960X_IIC_ADDR, addr, data, len);
    if (ret) {
        pr_err("sc8960x_write_block : write reg %02x fail\n", addr);
    }

    return ret;
}

static int sc8960x_read_block(struct sc8960x_chip *chip, 
        uint8_t addr, uint8_t *data, uint8_t len)
{
    int ret;

    // 使用Quectel I2C接口读取数据
    ret = ql_i2c_read(chip->i2c_channel, SC8960X_IIC_ADDR, addr, data, len);
    if (ret) {
        pr_err("sc8960x_read_block : read reg %02x fail\n", addr);
    }

    return ret;
}

static int sc8960x_reg_write(struct sc8960x_chip *chip,
        uint8_t addr, uint8_t data)
{
    return sc8960x_write_block(chip, addr, &data, 1);
}

static int sc8960x_reg_read(struct sc8960x_chip *chip,
        uint8_t addr, uint8_t *data)
{
    return sc8960x_read_block(chip, addr, data, 1);
}

static int sc8960x_reg_update_bits(struct sc8960x_chip *chip,
        uint8_t addr, uint8_t mask, uint8_t val)
{
    int ret;
    uint8_t reg_val;

    ret = sc8960x_reg_read(chip, addr, &reg_val);
    if (ret) {
        return -1;
    }

    reg_val &= ~mask;
    reg_val |= val;

    return sc8960x_reg_write(chip, addr, reg_val);
}

//---------------chip api---------------
static int sc8960x_detect_device(struct sc8960x_chip *chip)
{
    int ret;
    uint8_t data;

    ret = sc8960x_reg_read(chip, SC8960X_REG_0B, &data);
    if (!ret) {
        chip->pn_num = (data & REG0B_PN_MASK) >> REG0B_PN_SHIFT;
        /*if (chip->pn_num != SC89601D_PN) {
            pr_err("%s: not find sc89601d, pn = %d\n", __func__, chip->pn_num);
            return -1;
        }*/
    }

    return ret;
}

static int sc8960x_enable_otg(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG01_OTG_ENABLE : REG01_OTG_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_01, REG01_OTG_CONFIG_MASK,
                                val << REG01_OTG_CONFIG_SHIFT);
}

static int sc8960x_enable_charger(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG01_CHG_ENABLE : REG01_CHG_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_01, REG01_CHG_CONFIG_MASK, 
                                val << REG01_CHG_CONFIG_SHIFT);
}

static int sc8960x_set_charge_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t ichg;

    if (curr < REG02_ICHG_BASE) {
        curr = REG02_ICHG_BASE;
    } else if (curr > 3000) {
        curr = 3000;
    }

    pr_info("%s: %dmA\n", __func__, curr);

    ichg = (curr - REG02_ICHG_BASE) / REG02_ICHG_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_02, REG02_ICHG_MASK,
                                ichg << REG02_ICHG_SHIFT);
}

static int sc8960x_set_term_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t iterm;

    if (curr < REG03_ITERM_BASE) {
        curr = REG03_ITERM_BASE;
    } else if (curr > 960) {
        curr = 960;
    }
        

    pr_info("%s: %dmA\n", __func__, curr);

    iterm = (curr - REG03_ITERM_BASE) / REG03_ITERM_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_03, REG03_ITERM_MASK,
                                iterm << REG03_ITERM_SHIFT);
}

static int sc8960x_set_prechg_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t iprechg;

    if (curr < REG03_IPRECHG_BASE) {
        curr = REG03_IPRECHG_BASE;
    } else if (curr > 960) {
        curr = 960;
    }
    
    pr_info("%s: %dmA\n", __func__, curr);

    iprechg = (curr - REG03_IPRECHG_BASE) / REG03_IPRECHG_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_03, REG03_IPRECHG_MASK,
                                iprechg << REG03_IPRECHG_SHIFT);
}

static int sc8960x_set_chargevolt(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    if (volt < REG04_VREG_BASE) {
        volt = REG04_VREG_BASE;
    } else if (volt > 4864) {
        volt = 4864;
    }
    
    pr_info("%s: %dmV\n", __func__, volt);

    val = (volt - REG04_VREG_BASE) / REG04_VREG_LSB;
    if((volt - REG04_VREG_BASE)%REG04_VREG_LSB > 10) val += 1;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_04, REG04_VREG_MASK,
                                val << REG04_VREG_SHIFT);
}

static int sc8960x_set_vindpm(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    pr_info("%s: %dmV\n", __func__, volt);

    if (volt < REG06_VINDPM_BASE) {
        val = 0;
    } else if (volt <= 5100) {
        val = (volt - REG06_VINDPM_BASE) / REG06_VINDPM_LSB;
    } else if (volt <= 8000) {
        val = 13;
    } else if (volt <= 8200) {
        val = 14;
    } else {
        val = 15;
    }

    return sc8960x_reg_update_bits(chip, SC8960X_REG_06, REG06_VINDPM_MASK,
                                val << REG06_VINDPM_SHIFT);
}

static int sc8960x_set_iindpm(struct sc8960x_chip *chip, int curr)
{
    uint8_t val;

    if (curr < REG00_IINLIM_BASE) {
        curr = REG00_IINLIM_BASE;
    } else if (curr > 3200) {
        curr = 3200;
    }

    val = (curr - REG00_IINLIM_BASE) / REG00_IINLIM_LSB;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_00, REG00_IINLIM_MASK,
                                val << REG00_IINLIM_SHIFT);
}

static int sc8960x_set_wdt(struct sc8960x_chip *chip, enum watchdog_t wdt)
{
    return sc8960x_reg_update_bits(chip, SC8960X_REG_05, REG05_WDT_MASK, 
                    wdt << REG05_WDT_SHIFT);
}

static int sc8960x_enter_hiz_mode(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG00_HIZ_ENABLE : REG00_HIZ_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_00, REG00_ENHIZ_MASK, 
                    val << REG00_ENHIZ_SHIFT);
}

static int sc8960x_enable_term(struct sc8960x_chip *chip, bool enable)
{
    uint8_t val = enable ? REG05_TERM_ENABLE : REG05_TERM_DISABLE;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_05, REG05_EN_TERM_MASK, 
                    val << REG05_EN_TERM_SHIFT);
}

static int sc8960x_set_boost_current(struct sc8960x_chip *chip, int curr)
{
    uint8_t val = REG02_BOOST_LIM_0P5A;

    if (curr == BOOSTI_1200) {
        val = REG02_BOOST_LIM_1P2A;
    }

    return sc8960x_reg_update_bits(chip, SC8960X_REG_02, REG02_BOOST_LIM_MASK,
                                val << REG02_BOOST_LIM_SHIFT);
}

static int sc8960x_set_boost_voltage(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    if (volt == BOOSTV_4850)
        val = REG06_BOOSTV_4P85V;
    else if (volt == BOOSTV_5150)
        val = REG06_BOOSTV_5P15V;
    else if (volt == BOOSTV_5300)
        val = REG06_BOOSTV_5P3V;
    else
        val = REG06_BOOSTV_5V;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_06, REG06_BOOSTV_MASK,
                                val << REG06_BOOSTV_SHIFT);
}

static int sc8960x_set_acovp_threshold(struct sc8960x_chip *chip, int volt)
{
    uint8_t val;

    if (volt == VAC_OVP_14000)
        val = REG06_OVP_14P0V;
    else if (volt == VAC_OVP_10500)
        val = REG06_OVP_10P5V;
    else if (volt == VAC_OVP_6500)
        val = REG06_OVP_6P5V;
    else
        val = REG06_OVP_5P5V;
    
    return sc8960x_reg_update_bits(chip, SC8960X_REG_06, REG06_OVP_MASK,
                                val << REG06_OVP_SHIFT);
}

static int sc8960x_set_vindpm_track(struct sc8960x_chip *chip, int track_volt)
{
    uint8_t val = track_volt;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_07, REG07_VDPM_BAT_TRACK_MASK,
                                val << REG07_VDPM_BAT_TRACK_SHIFT);
}

static int sc8960x_reset_chip(struct sc8960x_chip *chip)
{
    uint8_t val = REG0B_REG_RESET << REG0B_REG_RESET_SHIFT;

    return sc8960x_reg_update_bits(chip, SC8960X_REG_0B, REG0B_REG_RESET_MASK, val);
}

//---------------system api---------------
int hal_set_charger_hiz(bool enable)
{
    return sc8960x_enter_hiz_mode(chip, enable);
}

int hal_set_charger_enable(bool enable)
{
    return sc8960x_enable_charger(chip, enable);
}

int hal_set_charger_otg(bool enable)
{
    return sc8960x_enable_otg(chip, enable);
}

int hal_set_charger_current(int curr)
{
    return sc8960x_set_charge_current(chip, curr);
}

int hal_set_charger_iindpm(int curr)
{
    return sc8960x_set_iindpm(chip, curr);
}

int hal_set_charger_vindpm(int volt)
{
    return sc8960x_set_vindpm(chip, volt);
}

void sc8960x_dump_regs(void)
{
    int addr;
    uint8_t val;
    int ret;

    for (addr = 0x0; addr <= 0x0B; addr++) {
        ret = sc8960x_reg_read(chip, addr, &val);
        if (ret == 0)
            pr_info("%s: Reg[%02x] = 0x%02x\r\n", __func__, addr, val);
    }
}

static int sc8960x_hw_init(struct sc8960x_chip *chip)
{
    int ret;

    ret = sc8960x_reset_chip(chip);
    if (ret < 0)
        pr_err("%s: Failed to reset registers(%d)\n", __func__, ret);

    ret = sc8960x_set_wdt(chip, REG05_WDT_DISABLE);
    if (ret < 0)
        pr_err("%s: Failed to disable wdt timer(%d)\n", __func__, ret);

    ret = sc8960x_set_prechg_current(chip, chip->desc->iprechg);
    if (ret < 0)
        pr_err("%s: Failed to set prechg current(%d)\n", __func__, ret);

    ret = sc8960x_enable_term(chip, TRUE);
    if (ret < 0) 
        pr_err("%s: Failed to enable termination(%d)\n", __func__, ret);

    ret = sc8960x_set_term_current(chip, chip->desc->iterm);
    if (ret < 0)
        pr_err("%s: Failed to set termination current(%d)\n", __func__, ret);

    ret = sc8960x_set_boost_voltage(chip, chip->desc->boostv);
    if (ret < 0)
        pr_err("%s: Failed to set boost voltage(%d)\n", __func__, ret);

    ret = sc8960x_set_boost_current(chip, chip->desc->boosti);
    if (ret < 0)
        pr_err("%s: Failed to set boost current(%d)\n", __func__, ret);

    ret = sc8960x_set_acovp_threshold(chip, chip->desc->vacovp);
    if (ret < 0)
        pr_err("%s: Failed to set acovp threshold(%d)\n", __func__, ret);

    ret = sc8960x_set_chargevolt(chip, chip->desc->vbat);
    if (ret < 0)
        pr_err("%s: Failed to set charge volt(%d)\n", __func__, ret);

    ret = sc8960x_set_vindpm_track(chip, REG07_VDPM_BAT_TRACK_300MV);
    if (ret < 0)
        pr_err("%s: Failed to set vindpm track(%d)\n", __func__, ret);

    sc8960x_dump_regs();

    return ret;
}

int sc8960x_dev_init(void)
{
    int ret;

    pr_info("%s: ver: %s\n", __func__, SC8960X_DRIVER_VERSION);

    // 设置使用的I2C通道
    chip->i2c_channel = SC8960X_IIC_NUM;

    // 初始化I2C通道
    ret = ql_i2c_init(chip->i2c_channel, STANDARD_MODE);
    if (ret) {
        pr_err("%s: Failed to init i2c(%d)\n", __func__, ret);
        return ret;
    }

    ret = sc8960x_detect_device(chip);
    if (ret) {
        pr_err("%s: detect device failed(%d)\n", __func__, ret);
        return ret;
    }

    chip->desc = &default_desc;

    ret = sc8960x_hw_init(chip);
    
    pr_info("%s: sc8960x init %s\n", __func__, 
        (ret == 0) ? "successful" : "failed");

    return ret;
}
