#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "ql_dev.h"
#include "osa.h"
#include "ql_uart.h"
#include "ql_gpio.h"

/* 日志封装 */
#define KEY_LOG_INFO(fmt, ...) RTI_LOG("[KEY/INFO] " fmt "\r\n", ##__VA_ARGS__)
#define KEY_LOG_ERROR(fmt, ...) RTI_LOG("[KEY/ERROR] " fmt "\r\n", ##__VA_ARGS__)

/* 任务参数 */
#define KEY_SCAN_TASK_STACK_SIZE (1024)
#define KEY_SCAN_TASK_PRIORITY 28
#define KEY_SCAN_TASK_NAME "key_scan_task"
#define KEY_EVENT_TASK_STACK_SIZE (1024)
#define KEY_EVENT_TASK_PRIORITY 30
#define KEY_EVENT_TASK_NAME "key_event_task"
#define KEY_MSG_QUEUE_SIZE 16

/* 时间转换：ms <-> tick （1 tick = 5ms）*/
#define MS_TO_TICKS(ms) (((ms) + 4) / 5)

/* 业务相关时间阈值 */
#define SCAN_PERIOD_TICKS MS_TO_TICKS(10)           /* 10ms 轮询 */
#define DEBOUNCE_TICKS MS_TO_TICKS(50)              /* 50ms 防抖 */
#define LONG_PRESS_TICKS MS_TO_TICKS(3000)          /* 3s 长按 */
#define DOUBLE_CLICK_TIMEOUT_TICKS MS_TO_TICKS(300) /* 300ms 双击超时 */
#define EXIT_WAIT_TIMEOUT_TICKS MS_TO_TICKS(2000)   /* 2s 退出等待 */
#define TASK_CLEANUP_POLL_INTERVAL_TICKS (1)        /* 1 tick 任务清理轮询间隔 */

/* 按键类型 */
typedef enum
{
    KEY_TYPE_FACTORY_RESET = 0,
    KEY_TYPE_MAX
} xy_key_type_e;

/* 事件类型 */
typedef enum
{
    KEY_EVENT_SHORT_PRESS = 0,
    KEY_EVENT_LONG_PRESS,
    KEY_EVENT_DOUBLE_CLICK,
    KEY_EVENT_MAX
} xy_key_event_type_e;

/* 按键状态机状态 */
typedef enum
{
    KEY_STATE_IDLE = 0,
    KEY_STATE_PRESSED,
    KEY_STATE_WAIT_MULTI_CLICK
} key_state_e;

/* 按键配置 */
typedef struct
{
    ql_gpio_num_e gpio_pin;
    const char *name;
} xy_key_config_t;

/* 状态上下文 */
typedef void (*key_event_callback_t)(xy_key_type_e, xy_key_event_type_e);
typedef struct
{
    key_state_e state;
    uint32_t press_tick;
    uint32_t release_tick;
    uint8_t click_count;
    ql_gpio_level_e last_level;
    key_event_callback_t cb;
} key_ctx_t;

/* 消息队列结构 */
typedef enum
{
    KEY_MSG_EVENT = 0,
    KEY_MSG_EXIT
} key_msg_type_e;
typedef struct
{
    key_msg_type_e msg_type;
    xy_key_type_e key_type;
    xy_key_event_type_e event_type;
} key_msg_t;

/* 全局变量 */
static OSAMsgQRef s_key_msg_queue = NULL;
static volatile bool s_exit_flag = false;
static OSTaskRef s_scan_task = NULL;
static OSTaskRef s_event_task = NULL;
static uint8_t s_scan_stack[KEY_SCAN_TASK_STACK_SIZE];
static uint8_t s_event_stack[KEY_EVENT_TASK_STACK_SIZE];

static const xy_key_config_t s_key_cfgs[KEY_TYPE_MAX] = {
    [KEY_TYPE_FACTORY_RESET] = {.gpio_pin = GPIO_0, .name = "Factory_Reset"},
};

static key_ctx_t s_key_ctxs[KEY_TYPE_MAX];
static const char *s_event_names[KEY_EVENT_MAX] = {
    "SHORT_PRESS", "LONG_PRESS", "DOUBLE_CLICK"};

/* 业务函数：恢复出厂 */
static void do_factory_reset(void)
{
    KEY_LOG_INFO("Trigger factory reset...");
    int ret = ql_dev_restore_default();
    if (ret == 0)
    {
        KEY_LOG_INFO("Factory reset command sent.");
    }
    else
    {
        KEY_LOG_ERROR("Factory reset failed, ret=%d", ret);
    }
}

/* 默认回调 */
static void key_handle_event(xy_key_type_e kt, xy_key_event_type_e et)
{
    if (kt == KEY_TYPE_FACTORY_RESET && et == KEY_EVENT_LONG_PRESS)
    {
        do_factory_reset();
    }
}

/* 注册回调 */
static int xy_key_register_callback(xy_key_type_e kt, key_event_callback_t cb)
{
    if (kt >= KEY_TYPE_MAX || cb == NULL)
        return -1;
    s_key_ctxs[kt].cb = cb;
    return 0;
}

/* 发消息给事件任务 */
static void key_send_event(xy_key_type_e kt, xy_key_event_type_e ev)
{
    key_msg_t msg = {
        .msg_type = KEY_MSG_EVENT,
        .key_type = kt,
        .event_type = ev};
    KEY_LOG_INFO("Send event: %s on %s", s_event_names[ev], s_key_cfgs[kt].name);
    if (OSAMsgQSend(s_key_msg_queue, sizeof(msg), (UINT8 *)&msg, OS_NO_SUSPEND) != OS_SUCCESS)
    {
        KEY_LOG_ERROR("MsgQ full, lose event");
    }
}

/* 状态机 */
static void key_state_machine(xy_key_type_e kt, ql_gpio_level_e lvl, uint32_t tick)
{
    key_ctx_t *c = &s_key_ctxs[kt];
    switch (c->state)
    {
    case KEY_STATE_IDLE:
        if (c->last_level == GPIO_HIGH && lvl == GPIO_LOW)
        {
            c->state = KEY_STATE_PRESSED;
            c->press_tick = tick;
            c->click_count = 0;
        }
        break;
    case KEY_STATE_PRESSED:
        if (c->last_level == GPIO_LOW && lvl == GPIO_HIGH)
        {
            uint32_t dur = tick - c->press_tick;
            if (dur >= LONG_PRESS_TICKS)
            {
                key_send_event(kt, KEY_EVENT_LONG_PRESS);
                c->state = KEY_STATE_IDLE;
            }
            else if (dur >= DEBOUNCE_TICKS)
            {
                c->click_count++;
                if (c->click_count >= 2)
                {
                    key_send_event(kt, KEY_EVENT_DOUBLE_CLICK);
                    c->state = KEY_STATE_IDLE;
                }
                else
                {
                    c->release_tick = tick;
                    c->state = KEY_STATE_WAIT_MULTI_CLICK;
                }
            }
            else
            {
                /* 小于防抖，抖动忽略 */
                c->state = KEY_STATE_IDLE;
            }
        }
        break;
    case KEY_STATE_WAIT_MULTI_CLICK:
        if (c->last_level == GPIO_HIGH && lvl == GPIO_LOW)
        {
            /* 第二次按下 */
            c->state = KEY_STATE_PRESSED;
            c->press_tick = tick;
        }
        else if (tick - c->release_tick >= DOUBLE_CLICK_TIMEOUT_TICKS)
        {
            /* 超时当单击 */
            key_send_event(kt, KEY_EVENT_SHORT_PRESS);
            c->state = KEY_STATE_IDLE;
        }
        break;
    }
    c->last_level = lvl;
}

/* 事件处理任务 */
static void key_event_task_entry(UINT32 argc, void *argv)
{
    KEY_LOG_INFO("Event task start");
    key_msg_t msg;
    while (!s_exit_flag)
    {
        if (OSAMsgQRecv(s_key_msg_queue, (UINT8 *)&msg, sizeof(msg), OS_SUSPEND) == OS_SUCCESS)
        {
            if (msg.msg_type == KEY_MSG_EVENT)
            {
                key_event_callback_t cb = s_key_ctxs[msg.key_type].cb;
                if (cb)
                    cb(msg.key_type, msg.event_type);
            }
            else if (msg.msg_type == KEY_MSG_EXIT)
            {
                KEY_LOG_INFO("Event task exit msg received");
                break;
            }
        }
    }
    KEY_LOG_INFO("Event task exit");
    s_event_task = NULL;
    OSATaskDelete(NULL);
}

/* 扫描任务 */
static void key_scan_task_entry(UINT32 argc, void *argv)
{
    KEY_LOG_INFO("Scan task start");
    while (!s_exit_flag)
    {
        uint32_t now = OSAGetTicks();
        for (int i = 0; i < KEY_TYPE_MAX; i++)
        {
            ql_gpio_level_e lvl;
            if (ql_gpio_get_level(s_key_cfgs[i].gpio_pin, &lvl) == QL_GPIO_SUCCESS)
            {
                key_state_machine(i, lvl, now);
            }
        }
        OSATaskSleep(SCAN_PERIOD_TICKS);
    }
    KEY_LOG_INFO("Scan task exit");
    s_scan_task = NULL;
    OSATaskDelete(NULL);
}

/* 释放资源 */
static void cleanup_resources(void)
{
    s_exit_flag = true;
    /* 发送退出消息给事件任务 */
    if (s_key_msg_queue)
    {
        key_msg_t ex = {.msg_type = KEY_MSG_EXIT};
        if (OSAMsgQSend(s_key_msg_queue, sizeof(ex), (UINT8 *)&ex, OS_NO_SUSPEND) != OS_SUCCESS)
        {
            KEY_LOG_ERROR("Failed to send exit message to event task during cleanup");
        }
    }
    /* 等待任务退出或超时 */
    uint32_t st = OSAGetTicks();
    while ((s_scan_task || s_event_task) &&
           (OSAGetTicks() - st < EXIT_WAIT_TIMEOUT_TICKS))
    {
        OSATaskSleep(TASK_CLEANUP_POLL_INTERVAL_TICKS);
    }
    /* 强制清理 */
    if (s_scan_task)
    {
        OSATaskDelete(s_scan_task);
        s_scan_task = NULL;
    }
    if (s_event_task)
    {
        OSATaskDelete(s_event_task);
        s_event_task = NULL;
    }
    if (s_key_msg_queue)
    {
        OSAMsgQDelete(s_key_msg_queue);
        s_key_msg_queue = NULL;
    }
}

/* 初始化 */
int xy_key_init(void)
{
    if (s_key_msg_queue)
    {
        KEY_LOG_INFO("Already initialized");
        return 0;
    }
    KEY_LOG_INFO("Init key module");
    memset(s_key_ctxs, 0, sizeof(s_key_ctxs));
    for (int i = 0; i < KEY_TYPE_MAX; i++)
    {
        s_key_ctxs[i].state = KEY_STATE_IDLE;
        s_key_ctxs[i].last_level = GPIO_HIGH;
    }
    /* 默认回调 */
    xy_key_register_callback(KEY_TYPE_FACTORY_RESET, key_handle_event);
    /* GPIO init */
    for (int i = 0; i < KEY_TYPE_MAX; i++)
    {
        int ret = ql_gpio_init(s_key_cfgs[i].gpio_pin,
                               GPIO_INPUT, PULL_UP, GPIO_HIGH);
        if (ret != QL_GPIO_SUCCESS)
        {
            KEY_LOG_ERROR("Init %s fail, pin=%d", s_key_cfgs[i].name, s_key_cfgs[i].gpio_pin);
            return -1;
        }
        KEY_LOG_INFO("%s on GPIO%d", s_key_cfgs[i].name, s_key_cfgs[i].gpio_pin);
    }
    /* 创建队列 */
    if (OSAMsgQCreate(&s_key_msg_queue, (UINT8 *)"KeyMsgQ",
                      sizeof(key_msg_t), KEY_MSG_QUEUE_SIZE, OSA_FIFO) != OS_SUCCESS)
    {
        KEY_LOG_ERROR("Create MsgQ fail");
        goto fail;
    }
    /* 创建任务 */
    if (OSATaskCreate(&s_event_task, s_event_stack, sizeof(s_event_stack),
                      KEY_EVENT_TASK_PRIORITY, KEY_EVENT_TASK_NAME,
                      key_event_task_entry, NULL) != OS_SUCCESS)
    {
        KEY_LOG_ERROR("Create event task fail");
        goto fail;
    }
    if (OSATaskCreate(&s_scan_task, s_scan_stack, sizeof(s_scan_stack),
                      KEY_SCAN_TASK_PRIORITY, KEY_SCAN_TASK_NAME,
                      key_scan_task_entry, NULL) != OS_SUCCESS)
    {
        KEY_LOG_ERROR("Create scan task fail");
        goto fail;
    }
    KEY_LOG_INFO("Key module init ok");
    return 0;

fail:
    cleanup_resources();
    return -1;
}

/* 反初始化 */
int xy_key_deinit(void)
{
    if (!s_key_msg_queue)
        return 0;

    cleanup_resources();
    KEY_LOG_INFO("Key module deinit ok");
    return 0;
}
