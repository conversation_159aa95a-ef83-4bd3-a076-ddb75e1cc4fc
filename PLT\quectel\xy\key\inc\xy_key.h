#ifndef XY_KEY_H
#define XY_KEY_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* 按键类型 */
typedef enum
{
    KEY_TYPE_FACTORY_RESET = 0,
    KEY_TYPE_MAX
} xy_key_type_e;

/* 事件类型 */
typedef enum
{
    KEY_EVENT_SHORT_PRESS = 0,
    KEY_EVENT_LONG_PRESS,
    KEY_EVENT_DOUBLE_CLICK,
    KEY_EVENT_MAX
} xy_key_event_type_e;

/* 按键事件回调函数类型 */
typedef void (*key_event_callback_t)(xy_key_type_e key_type, xy_key_event_type_e event_type);

/**
 * @brief 初始化按键模块
 * @return 0: 成功, -1: 失败
 */
int xy_key_init(void);

/**
 * @brief 反初始化按键模块
 * @return 0: 成功, -1: 失败
 */
int xy_key_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* XY_KEY_H */